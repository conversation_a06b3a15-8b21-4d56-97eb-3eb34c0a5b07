import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@core/components/ui/sidebar";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";
import { AppSidebar } from "@core/components/app-sidebar";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@core/components/ui/select";

import { Label } from "@core/components/ui/label";

import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@core/lib/utils";
import { Button } from "@core/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@core/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@core/components/ui/popover";

import MapperLib from "core/MapperLib";
import MapperSkillsLibHeader from "core/Mapper/MapperSkillsLibHeader";
import MapperSkillRow from "core/Mapper/MapperSkillRow";

export default function Dashboard({ behavioural_skills, technical_skills }) {
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [userData, setUserData] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [userRoles, setUserRoles] = useState(null);
  const [typeSelected, setTypeSelected] = useState(null);
  const [roleSelected, setRoleSelected] = useState(null);
  const [skillSelected, setSkillSelected] = useState(null);
  const [skillSelectedId, setSkillSelectedId] = useState(null);
  const [open, setOpen] = useState(null);
  const [value, setValue] = useState(null);

  const router = useRouter();

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if no session
        router.push("/");
      }
    });

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if session is lost
        router.push("/");
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Fetch user profile when session is available
  useEffect(() => {
    if (session) {
      getUserProfile();
    }
  }, [session]);

  /* --- DEBUG --- */

  // console.log("typeSelected");
  // console.log(typeSelected);

  // console.log("roleSelected");
  // console.log(roleSelected);

  // console.log("skillSelected");
  // console.log(skillSelected);

  // console.log("userData");
  // console.log(userData);

  // console.log("userRole");
  // console.log(userRole);

  // console.log("userRoles");
  // console.log(userRoles);

  // console.log("behavioural_skills");
  // console.log(behavioural_skills);

  // console.log("technical_skills");
  // console.log(technical_skills);

  /* --- DEBUG --- */

  const getSelectedRecord = (skill_id) => {
    console.log("skill_id");
    console.log(skill_id);

    // if behavioural or technical, get the record from the array

    if (typeSelected === "Behavioural") {
      const selectedSkillsRec = behavioural_skills.find(
        (record) => record.id === skill_id
      );

      console.log("found array");
      console.log(selectedSkillsRec);

      return selectedSkillsRec;
    }

    if (typeSelected === "Technical") {
      const selectedSkillsRec = technical_skills.find(
        (record) => record.id === skill_id
      );

      console.log("found array");
      console.log(selectedSkillsRec);

      return selectedSkillsRec;
    }

    // const selectedSkillsRec = technical_skills.find(
    //   (record) => record.id === skill_id
    // );

    console.log("found array");
    console.log(selectedSkillsRec);

    return selectedSkillsRec;

    // return arId.id - 1;
  };

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);
        getUserRole(data.team_role);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // get user's role
  async function getUserRole(id) {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("roles")
        .select("id, team")
        .eq("id", id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserRole(data);
        getRoles(data.team);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // get roles based on user
  async function getRoles(team) {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("roles")
        .select("id, team, role_name")
        .eq("team", team);

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserRoles(data);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      </div>
    );
  }

  // Don't render dashboard if no session (will redirect)
  if (!session) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar
        userData={userData}
        behavioural_skills={behavioural_skills}
        technical_skills={technical_skills}
      />

      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-3">
            <SidebarTrigger />

            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">Admin tools</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink>Skills mapping</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <main>
          {/* <div className="flex flex-1 flex-col gap-2 p-2">
            <div className="grid grid-cols-[10%_18%_18%_18%_18%_18%]">test</div>
          </div>
           */}
          <div className="flex flex-1 flex-row gap-2 p-2 pt-3">
            <div className="flex m-auto">
              <div className="ml-6 pt-0">
                <Select>
                  <Label
                    className="text-sm text-center pb-1 text-primary font-semibold"
                    htmlFor="skill type"
                  >
                    Role
                  </Label>
                  <SelectTrigger className="w-[300px]">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {userRoles &&
                      userRoles.map((role) => {
                        if (role.role_name !== "Team Owner") {
                          return (
                            <SelectItem key={role.id} value={role.id}>
                              {role.role_name}
                            </SelectItem>
                          );
                        }
                      })}
                  </SelectContent>
                </Select>
              </div>

              <div className="ml-6 pt-0">
                <Select
                  onValueChange={(skillSelected) =>
                    setTypeSelected(skillSelected)
                  }
                >
                  <Label
                    className="text-sm text-center pb-1  text-primary font-semibold"
                    htmlFor="skill type"
                  >
                    Skill type
                  </Label>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Behavioural">Behavioural</SelectItem>
                    <SelectItem value="Technical">Technical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="ml-6 pt-0"></div>
              <div className="ml-6 pt-0">
                {!typeSelected && (
                  <Select disabled>
                    <Label
                      className="text-sm text-center pb-1 text-primary font-semibold"
                      htmlFor="skill type"
                    >
                      Skill
                    </Label>
                    <SelectTrigger className="w-[220px]">
                      <SelectValue placeholder="Select a skill" />
                    </SelectTrigger>
                  </Select>
                )}

                {typeSelected === "Behavioural" && (
                  <Popover open={open} onOpenChange={setOpen}>
                    <Label
                      className="text-sm text-center pb-1 text-primary font-semibold"
                      htmlFor="skill type"
                    >
                      Skill
                    </Label>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className="w-[300px] justify-between text-muted-foreground"
                      >
                        {skillSelected
                          ? behavioural_skills.find(
                              (skill) => skill.skill_name === skillSelected
                            )?.skill_name
                          : "Select skill"}
                        <ChevronsUpDown className="opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[200px] p-0">
                      <Command>
                        <CommandInput
                          placeholder="Search skills..."
                          className="h-9"
                        />
                        <CommandList>
                          <CommandEmpty>No match found</CommandEmpty>
                          <CommandGroup>
                            {behavioural_skills.map((skill) => (
                              <CommandItem
                                key={skill.skill_name}
                                value={skill.skill_name}
                                onSelect={(currentValue) => {
                                  // setSkillSelected(
                                  //   currentValue === skillSelected
                                  //     ? ""
                                  //     : currentValue
                                  // );
                                  setSkillSelectedId(
                                    getSelectedRecord(skill.id)
                                  );
                                  setOpen(false);
                                }}
                              >
                                {skill.skill_name}
                                <Check
                                  className={cn(
                                    "ml-auto",
                                    skillSelected === skill.skill_name
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                )}

                {typeSelected === "Technical" && (
                  <Popover open={open} onOpenChange={setOpen}>
                    <Label
                      className="text-sm text-center pb-1 text-primary font-semibold"
                      htmlFor="skill type"
                    >
                      Skill
                    </Label>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className="w-[300px] justify-between"
                      >
                        {skillSelected
                          ? technical_skills.find(
                              (skill) => skill.skill_name === skillSelected
                            )?.skill_name
                          : "Select skill"}
                        <ChevronsUpDown className="opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[200px] p-0">
                      <Command>
                        <CommandInput
                          placeholder="Search skills..."
                          className="h-9"
                        />
                        <CommandList>
                          <CommandEmpty>No match found</CommandEmpty>
                          <CommandGroup>
                            {technical_skills.map((skill) => (
                              <CommandItem
                                key={skill.skill_name}
                                value={skill.skill_name}
                                onSelect={(currentValue) => {
                                  setSkillSelected(
                                    currentValue === skillSelected
                                      ? ""
                                      : currentValue
                                  );

                                  setSkillSelectedId(
                                    getSelectedRecord(skill.id)
                                  );
                                  setOpen(false);
                                }}
                              >
                                {skill.skill_name}

                                <Check
                                  className={cn(
                                    "ml-auto",
                                    skillSelected === skill.skill_name
                                      ? "opacity-100"
                                      : "opacity-0"
                                  )}
                                />
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                )}
              </div>
            </div>
          </div>

          {skillSelected && (
            <MapperLib>
              <MapperSkillsLibHeader />
              <MapperSkillRow
                technical_sub_skills={skillSelectedId.technical_sub_skills}
                skillSelected={skillSelected}
              />
            </MapperLib>
          )}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}

export const getStaticProps = async () => {
  const bdata = await supabase
    .from("behavioural_skills")
    .select(
      "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name,level_1_description, level_2_description, level_3_description, level_4_description, level_5_description, index)"
    );

  const techdata = await supabase
    .from("technical_skills")
    .select(
      "id, skill_name, technical_sub_skills (id, sub_skill_name,level_1_description, level_2_description, level_3_description, level_4_description, index)"
    );

  const responses = await Promise.all([bdata, techdata]);

  return {
    props: {
      behavioural_skills: responses[0].data,
      technical_skills: responses[1].data,
    },
  };
};
