"use client";

import { ChevronRight, Combine } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@core/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@core/components/ui/sidebar";
import Link from "next/link";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@core/components/ui/select";

export function NavAdmin({ userData }) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel className="min-w-8 bg-secondary text-primary-foreground">
        Admin tools
      </SidebarGroupLabel>
      <SidebarMenu>
        <Collapsible
          key={1}
          asChild
          defaultOpen={false}
          className="group/collapsible"
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton tooltip={"Admin tools"}>
                <Combine />
                <Link href="/admin/skills-mapping">
                  <span>Skills mapping</span>{" "}
                </Link>

                {/* <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" /> */}

                <ChevronRight className="ml-auto" />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            {/* <CollapsibleContent>
              <div className="ml-6 pt-0">
                <Select
                  onValueChange={(value) => {
                    console.log(value);
                  }}
                >
                  <SelectTrigger className="w-[220px]">
                    <SelectValue placeholder="Select a Function" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Security">Security</SelectItem>
                    <SelectItem value="Another Security">
                      Another Security
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CollapsibleContent> */}

            {/* <CollapsibleContent>
              <SidebarMenuSub>
                  {item.items?.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.title}>
                      <SidebarMenuSubButton asChild>
                        <a href={subItem.url}>
                          <span>{subItem.title}</span>
                        </a>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
            </CollapsibleContent> */}
          </SidebarMenuItem>
        </Collapsible>
      </SidebarMenu>
    </SidebarGroup>
  );
}
